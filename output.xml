<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.10.11 on win32)" generated="2025-05-28T19:19:31.026121" rpa="false" schemaversion="5">
<suite id="s1" name="Create Booking Suite" source="C:\Users\<USER>\Documents\excel_robot_day5\create_booking_suite.robot">
<kw name="Clean Workbooks" type="SETUP">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T19:19:31.429901" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T19:19:31.429901" elapsed="0.000000"/>
</kw>
<doc>MỤC ĐÍCH: <PERSON><PERSON><PERSON> tất cả workbook Excel đang mở để tránh xung đột</doc>
<status status="PASS" start="2025-05-28T19:19:31.429901" elapsed="0.000000"/>
</kw>
<test id="s1-t1" name="Get Booking And Write To Excel" line="51">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:31.446151" level="INFO">BƯỚC 1: Gửi GET request để lấy thông tin booking ID 1636</msg>
<arg>BƯỚC 1: Gửi GET request để lấy thông tin booking ID 1636</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="0.000000"/>
</kw>
<kw name="Get Booking By ID">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:31.446151" level="INFO">🔍 Chuẩn bị lấy thông tin booking với ID: 1636</msg>
<arg>🔍 Chuẩn bị lấy thông tin booking với ID: ${booking_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T19:19:31.446151" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:31.446151" level="INFO">🌐 Gửi GET request đến: https://restful-booker.herokuapp.com/booking/1636</msg>
<arg>🌐 Gửi GET request đến: ${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="0.000000"/>
</kw>
<kw name="GET" owner="RequestsLibrary">
<msg time="2025-05-28T19:19:32.601333" level="INFO">GET Request : url=https://restful-booker.herokuapp.com/booking/1636 
 path_url=/booking/1636 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'} 
 body=None 
 </msg>
<msg time="2025-05-28T19:19:32.601333" level="INFO">GET Response : url=https://restful-booker.herokuapp.com/booking/1636 
 status=404, reason=Not Found 
 headers={'Content-Length': '9', 'Content-Type': 'text/plain; charset=utf-8', 'Date': 'Wed, 28 May 2025 12:19:32 GMT', 'Etag': 'W/"9-0gXL1ngzMqISxa6S1zx3F4wtLyg"', 'Nel': '{"report_to":"heroku-nel","response_headers":["Via"],"max_age":3600,"success_fraction":0.01,"failure_fraction":0.1}', 'Report-To': '{"group":"heroku-nel","endpoints":[{"url":"https://nel.heroku.com/reports?s=j2vI%2F%2Fa1222TbOfygv55u4mKceQSPjhKe67HphpQddg%3D\\u0026sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add\\u0026ts=1748434772"}],"max_age":3600}', 'Reporting-Endpoints': 'heroku-nel="https://nel.heroku.com/reports?s=j2vI%2F%2Fa1222TbOfygv55u4mKceQSPjhKe67HphpQddg%3D&amp;sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&amp;ts=1748434772"', 'Server': 'Heroku', 'Via': '1.1 heroku-router', 'X-Powered-By': 'Express'} 
 body=Not Found 
 </msg>
<msg time="2025-05-28T19:19:32.601333" level="INFO">${api_response} = &lt;Response [404]&gt;</msg>
<var>${api_response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a GET request.</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="1.155182"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:32.601333" level="INFO">📥 Response status: 404</msg>
<arg>📥 Response status: ${api_response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:32.601333" level="INFO">📥 Response body: Not Found</msg>
<arg>📥 Response body: ${api_response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<return>
<value>${api_response}</value>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</return>
<msg time="2025-05-28T19:19:32.601333" level="INFO">${api_response} = &lt;Response [404]&gt;</msg>
<var>${api_response}</var>
<arg>1636</arg>
<doc>MỤC ĐÍCH: Lấy thông tin booking từ API theo booking ID</doc>
<status status="PASS" start="2025-05-28T19:19:31.446151" elapsed="1.155182"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:32.601333" level="INFO">BƯỚC 2: Xác thực response từ API</msg>
<arg>BƯỚC 2: Xác thực response từ API</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Verify Get Booking Response">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T19:19:32.601333" level="INFO">🔍 Kiểm tra status code của GET response</msg>
<arg>🔍 Kiểm tra status code của GET response</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${api_response.status_code} == 404">
<kw name="Fail" owner="BuiltIn">
<msg time="2025-05-28T19:19:32.601333" level="FAIL">❌ Booking không tồn tại (404 Not Found)</msg>
<arg>❌ Booking không tồn tại (404 Not Found)</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="FAIL" start="2025-05-28T19:19:32.601333" elapsed="0.000000">❌ Booking không tồn tại (404 Not Found)</status>
</kw>
<status status="FAIL" start="2025-05-28T19:19:32.601333" elapsed="0.000000">❌ Booking không tồn tại (404 Not Found)</status>
</branch>
<branch type="ELSE IF" condition="${api_response.status_code} != 200">
<kw name="Fail" owner="BuiltIn">
<arg>❌ API call thất bại với status code: ${api_response.status_code}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</branch>
<status status="FAIL" start="2025-05-28T19:19:32.601333" elapsed="0.000000">❌ Booking không tồn tại (404 Not Found)</status>
</if>
<kw name="Log" owner="BuiltIn">
<arg>✅ Status code hợp lệ: ${api_response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${response_body_dict}</var>
<arg>${api_response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>🔍 Đã parse JSON response thành dictionary</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>🔍 Kiểm tra các field bắt buộc trong response</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}</arg>
<arg>firstname</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}</arg>
<arg>lastname</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}</arg>
<arg>totalprice</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}</arg>
<arg>depositpaid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body_dict}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>✅ Tất cả field bắt buộc đều có trong response</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${customer_name}</var>
<arg>${response_body_dict}[firstname] ${response_body_dict}[lastname]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>✅ Xác thực thành công cho booking của: ${customer_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<return>
<value>${response_body_dict}</value>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</return>
<var>${validated_booking_data}</var>
<arg>${api_response}</arg>
<doc>MỤC ĐÍCH: Xác thực response từ GET booking API</doc>
<status status="FAIL" start="2025-05-28T19:19:32.601333" elapsed="0.000000">❌ Booking không tồn tại (404 Not Found)</status>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>BƯỚC 3: Ghi dữ liệu booking vào Excel</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Write Booking Data To Excel">
<arg>${validated_booking_data}</arg>
<arg>1636</arg>
<doc>MỤC ĐÍCH: Ghi dữ liệu booking vào Excel sheet mới với format có cấu trúc</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>🎉 Hoàn thành lấy dữ liệu từ API và ghi vào Excel</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T19:19:32.601333" elapsed="0.000000"/>
</kw>
<kw name="Clean Workbooks" type="TEARDOWN">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T19:19:32.616562" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.015229"/>
</kw>
<doc>MỤC ĐÍCH: Đóng tất cả workbook Excel đang mở để tránh xung đột</doc>
<status status="PASS" start="2025-05-28T19:19:32.601333" elapsed="0.015229"/>
</kw>
<doc>MỤC ĐÍCH: Học cách lấy dữ liệu từ API và ghi vào Excel

QUY TRÌNH:
1. Gửi GET request để lấy thông tin booking theo ID
2. Xác thực và xử lý JSON response
3. Tạo sheet mới trong Excel
4. Ghi dữ liệu booking vào Excel theo format có cấu trúc

HỌC ĐƯỢC:
- Cách gửi GET request và xử lý response
- Cách tạo sheet mới trong Excel
- Cách ghi dữ liệu có cấu trúc vào Excel
- Cách xử lý lỗi API (404, 500, etc.)</doc>
<status status="FAIL" start="2025-05-28T19:19:31.445604" elapsed="1.170958">❌ Booking không tồn tại (404 Not Found)</status>
</test>
<kw name="Clean Workbooks" type="TEARDOWN">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T19:19:32.618822" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T19:19:32.617824" elapsed="0.000998"/>
</kw>
<doc>MỤC ĐÍCH: Đóng tất cả workbook Excel đang mở để tránh xung đột</doc>
<status status="PASS" start="2025-05-28T19:19:32.617824" elapsed="0.000998"/>
</kw>
<doc>Test suite để học cách kiểm thử API CreateBooking và tích hợp Excel
Bao gồm: Tạo booking từ dữ liệu Excel, Lấy thông tin booking và ghi vào Excel</doc>
<status status="FAIL" start="2025-05-28T19:19:31.026121" elapsed="1.592701"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Create Booking Suite" id="s1" pass="0" fail="1" skip="0">Create Booking Suite</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
