# =============================================================================
# ROBOT FRAMEWORK TEST SUITE - BOOKING API AUTOMATION
# =============================================================================
# Đây là một test suite hoàn chỉnh để học cách:
# - Kiểm thử REST API với Robot Framework
# - Tích hợp Excel để đọc/ghi dữ liệu test
# - Xử lý JSON response từ API
# - Quản lý lỗi và validation dữ liệu
# =============================================================================

*** Settings ***
# PHẦN SETTINGS: Cấu hình chung cho toàn bộ test suite
Documentation    Test suite để học cách kiểm thử API CreateBooking và tích hợp Excel
...              Bao gồm: Tạo booking từ dữ liệu Excel, Lấy thông tin booking và ghi vào Excel

# IMPORT CÁC THỨ VIỆN CẦN THIẾT:
Library    RequestsLibrary        # Thư viện để gửi HTTP requests (GET, POST, PUT, DELETE)
Library    ExcellentLibrary       # Thư viện để làm việc với file Excel (.xlsx)
Library    Collections            # Thư viện để làm việc với Dictionary, List
Library    DateTime               # Thư viện để xử lý ngày tháng
Library    String                 # Thư viện để xử lý chuỗi ký tự
Library    JSONLibrary            # Thư viện để xử lý dữ liệu JSON

# SETUP VÀ TEARDOWN: Chuẩn bị và dọn dẹp trước/sau khi chạy test
Suite Setup       Clean Workbooks    # Chạy trước khi bắt đầu toàn bộ test suite
Suite Teardown    Clean Workbooks    # Chạy sau khi kết thúc toàn bộ test suite
Test Teardown     Clean Workbooks    # Chạy sau mỗi test case để dọn dẹp

*** Variables ***
# PHẦN VARIABLES: Định nghĩa các biến toàn cục sử dụng trong test suite

# API CONFIGURATION - Cấu hình thông tin API
${BASE_URL}           https://restful-booker.herokuapp.com    # URL gốc của API
${BOOKING_ENDPOINT}   /booking                                # Endpoint để thao tác với booking

# FILE PATHS - Đường dẫn file
${EXCEL_FILE}         ${CURDIR}${/}resources${/}booking_data.xlsx    # File Excel chứa test data

# HTTP HEADERS - Header cho API requests
${HEADERS}            {"Content-Type": "application/json", "Accept": "application/json"}

# =============================================================================
# PHẦN TEST CASES: Các kịch bản kiểm thử chính
# =============================================================================

*** Test Cases ***


# TEST CASE : LẤY THÔNG TIN BOOKING VÀ GHI VÀO EXCEL
# -----------------------------------------------------------------------------
Get Booking And Write To Excel
    [Documentation]
    ...    MỤC ĐÍCH: Học cách lấy dữ liệu từ API và ghi vào Excel
    ...
    ...    QUY TRÌNH:
    ...    1. Gửi GET request để lấy thông tin booking theo ID
    ...    2. Xác thực và xử lý JSON response
    ...    3. Tạo sheet mới trong Excel
    ...    4. Ghi dữ liệu booking vào Excel theo format có cấu trúc
    ...
    ...    HỌC ĐƯỢC:
    ...    - Cách gửi GET request và xử lý response
    ...    - Cách tạo sheet mới trong Excel
    ...    - Cách ghi dữ liệu có cấu trúc vào Excel
    ...    - Cách xử lý lỗi API (404, 500, etc.)


    # BƯỚC 1: LẤY THÔNG TIN BOOKING TỪ API
    # Sử dụng booking ID có sẵn trong hệ thống (1636 là ID hợp lệ)
    Log    BƯỚC 1: Gửi GET request để lấy thông tin booking ID 1636
    ${api_response}=    Get Booking By ID    1636

    # BƯỚC 2: XÁC THỰC VÀ XỬ LÝ RESPONSE
    # Kiểm tra status code, format JSON và các field bắt buộc
    Log    BƯỚC 2: Xác thực response từ API
    ${validated_booking_data}=    Verify Get Booking Response    ${api_response}

    # BƯỚC 3: GHI DỮ LIỆU VÀO EXCEL
    # Tạo sheet mới và ghi dữ liệu theo format có cấu trúc
    Log    BƯỚC 3: Ghi dữ liệu booking vào Excel
    Write Booking Data To Excel    ${validated_booking_data}    1636

    Log   Hoàn thành lấy dữ liệu từ API và ghi vào Excel

# =============================================================================
# PHẦN KEYWORDS: Các từ khóa tùy chỉnh (Custom Keywords)
# =============================================================================
# Keywords là các hàm tùy chỉnh để thực hiện các tác vụ cụ thể
# Giúp code dễ đọc, dễ bảo trì và có thể tái sử dụng

*** Keywords ***

# KEYWORD: DỌN DẸP WORKBOOK
# -----------------------------------------------------------------------------
Clean Workbooks
    [Documentation]
    ...    MỤC ĐÍCH: Đóng tất cả workbook Excel đang mở để tránh xung đột
    ...
    ...    SỬ DỤNG: Được gọi trong Suite Setup, Suite Teardown, Test Teardown
    ...    để đảm bảo không có workbook nào bị "treo" giữa các test
    ...
    ...    LƯU Ý: Sử dụng "Run Keyword And Ignore Error" để tránh lỗi
    ...    khi không có workbook nào đang mở

    # Đóng tất cả workbook, bỏ qua lỗi nếu không có workbook nào mở
    Run Keyword And Ignore Error    Close All Workbooks


# KEYWORD: TẠO BOOKING QUA API
# -----------------------------------------------------------------------------
Create Booking
    [Documentation]
    ...    MỤC ĐÍCH: Gửi POST request để tạo booking mới qua API
    ...
    ...    INPUT: Dictionary chứa thông tin booking từ Excel
    ...    - firstname: Tên khách hàng
    ...    - lastname: Họ khách hàng
    ...    - totalprice: Tổng giá tiền
    ...    - depositpaid: Đã đặt cọc hay chưa (True/False)
    ...    - checkin_date: Ngày check-in
    ...    - checkout_date: Ngày check-out
    ...    - additionalneeds: Yêu cầu đặc biệt
    ...
    ...    OUTPUT: Response object từ API
    ...
    ...    API ENDPOINT: POST /booking
    ...    EXPECTED RESPONSE: 200 OK với booking ID và thông tin booking

    [Arguments]    ${booking_data_from_excel}

    # BƯỚC 1: CHUẨN BỊ DỮ LIỆU NGÀY THÁNG
    # Lấy ngày check-in và check-out từ Excel data
    Log    Chuẩn bị dữ liệu booking từ Excel
    ${checkin_date}=     Set Variable    ${booking_data_from_excel}[checkin_date]
    ${checkout_date}=    Set Variable    ${booking_data_from_excel}[checkout_date]

    # BƯỚC 2: TẠO OBJECT BOOKINGDATES
    # API yêu cầu bookingdates phải là một object riêng biệt
    ${bookingdates_object}=    Create Dictionary
    ...    checkin=${checkin_date}
    ...    checkout=${checkout_date}

    # BƯỚC 3: TẠO PAYLOAD CHO REQUEST
    # Tạo dictionary chứa tất cả thông tin booking theo format API yêu cầu
    ${api_payload}=    Create Dictionary
    ...    firstname=${booking_data_from_excel}[firstname]
    ...    lastname=${booking_data_from_excel}[lastname]
    ...    totalprice=${booking_data_from_excel}[totalprice]
    ...    depositpaid=${booking_data_from_excel}[depositpaid]
    ...    bookingdates=${bookingdates_object}
    ...    additionalneeds=${booking_data_from_excel}[additionalneeds]

    # BƯỚC 4: LOG PAYLOAD ĐỂ DEBUG
    # Chuyển payload thành JSON string để dễ đọc trong log
    ${payload_json_string}=    Evaluate    json.dumps($api_payload, indent=4)    json
    Log   Payload gửi đến API:\n${payload_json_string}

    # BƯỚC 5: CHUẨN BỊ HEADERS
    # Chuyển string headers thành dictionary để sử dụng trong request
    ${headers_dict}=    Evaluate    json.loads('''${HEADERS}''')    json

    # BƯỚC 6: GỬI POST REQUEST
    # Gửi request tạo booking đến API
    Log    Gửi POST request đến: ${BASE_URL}${BOOKING_ENDPOINT}
    ${api_response}=    POST    ${BASE_URL}${BOOKING_ENDPOINT}
    ...    json=${api_payload}           # Gửi payload dưới dạng JSON
    ...    headers=${headers_dict}       # Gửi kèm headers
    ...    expected_status=any           # Chấp nhận mọi status code để xử lý sau

    # BƯỚC 7: LOG RESPONSE ĐỂ DEBUG
    Log    Response status: ${api_response.status_code}
    Log   Response body: ${api_response.text}

    # BƯỚC 8: TRẢ VỀ RESPONSE
    RETURN    ${api_response}


# KEYWORD: XÁC THỰC RESPONSE TẠO BOOKING
# -----------------------------------------------------------------------------
Verify Booking Response
    [Documentation]
    ...    MỤC ĐÍCH: Xác thực response từ API tạo booking
    ...
    ...    INPUT:
    ...    - api_response: Response object từ POST /booking
    ...    - original_booking_data: Dữ liệu booking gốc từ Excel để so sánh
    ...
    ...    VALIDATION:
    ...    - Status code phải là 200
    ...    - Response phải chứa bookingid
    ...    - Response phải chứa booking object
    ...    - Dữ liệu trong response phải khớp với dữ liệu gửi đi
    ...
    ...    ERROR HANDLING: Fail test nếu validation không thành công

    [Arguments]    ${api_response}    ${original_booking_data}

    # BƯỚC 1: XÁC THỰC STATUS CODE
    # API tạo booking thành công phải trả về status code 200
    Log    Kiểm tra status code của response
    Should Be Equal As Strings    ${api_response.status_code}    200
    Log    Status code hợp lệ: ${api_response.status_code}

    # BƯỚC 2: CHUYỂN RESPONSE BODY THÀNH DICTIONARY
    # Parse JSON response thành Python dictionary để dễ xử lý
    ${response_body_dict}=    Set Variable    ${api_response.json()}
    Log     Đã parse JSON response thành dictionary

    # BƯỚC 3: XÁC THỰC CẤU TRÚC RESPONSE
    # Kiểm tra response có chứa các field bắt buộc
    Log     Kiểm tra cấu trúc response
    Dictionary Should Contain Key    ${response_body_dict}    bookingid
    Dictionary Should Contain Key    ${response_body_dict}    booking
    Log     Response có cấu trúc hợp lệ

    # BƯỚC 4: LẤY THÔNG TIN BOOKING TỪ RESPONSE
    ${booking_from_response}=    Set Variable    ${response_body_dict}[booking]

    # BƯỚC 5: XÁC THỰC DỮ LIỆU BOOKING
    # So sánh dữ liệu trong response với dữ liệu gốc từ Excel
    Log     So sánh dữ liệu booking với dữ liệu gốc

    # Kiểm tra thông tin cá nhân
    Should Be Equal As Strings    ${booking_from_response}[firstname]    ${original_booking_data}[firstname]
    Should Be Equal As Strings    ${booking_from_response}[lastname]     ${original_booking_data}[lastname]

    # Kiểm tra thông tin giá cả
    Should Be Equal As Numbers    ${booking_from_response}[totalprice]   ${original_booking_data}[totalprice]
    Should Be Equal               ${booking_from_response}[depositpaid]  ${original_booking_data}[depositpaid]

    # Kiểm tra yêu cầu đặc biệt
    Should Be Equal As Strings    ${booking_from_response}[additionalneeds]    ${original_booking_data}[additionalneeds]

    # BƯỚC 6: XÁC THỰC THÔNG TIN NGÀY THÁNG
    # Kiểm tra bookingdates object
    Log    Kiểm tra thông tin ngày tháng
    Dictionary Should Contain Key    ${booking_from_response}    bookingdates
    Dictionary Should Contain Key    ${booking_from_response}[bookingdates]    checkin
    Dictionary Should Contain Key    ${booking_from_response}[bookingdates]    checkout

    # So sánh ngày check-in và check-out
    Should Be Equal As Strings    ${booking_from_response}[bookingdates][checkin]     ${original_booking_data}[checkin_date]
    Should Be Equal As Strings    ${booking_from_response}[bookingdates][checkout]    ${original_booking_data}[checkout_date]

    # BƯỚC 7: LOG KẾT QUẢ THÀNH CÔNG
    ${booking_id}=    Set Variable    ${response_body_dict}[bookingid]
    Log    Xác thực thành công! Booking ID: ${booking_id} cho ${original_booking_data}[firstname] ${original_booking_data}[lastname]


# KEYWORD: LẤY THÔNG TIN BOOKING THEO ID
# -----------------------------------------------------------------------------
Get Booking By ID
    [Documentation]
    ...    MỤC ĐÍCH: Lấy thông tin booking từ API theo booking ID
    ...
    ...    INPUT: booking_id (số ID của booking cần lấy)
    ...
    ...    OUTPUT: Response object từ API
    ...
    ...    API ENDPOINT: GET /booking/{id}
    ...    EXPECTED RESPONSE: 200 OK với thông tin booking
    ...    POSSIBLE ERRORS: 404 Not Found nếu booking không tồn tại

    [Arguments]    ${booking_id}

    # BƯỚC 1: LOG THÔNG TIN REQUEST
    Log     Chuẩn bị lấy thông tin booking với ID: ${booking_id}

    # BƯỚC 2: CHUẨN BỊ HEADERS
    # Chuyển string headers thành dictionary
    ${headers_dict}=    Evaluate    json.loads('''${HEADERS}''')    json

    # BƯỚC 3: GỬI GET REQUEST
    # Gửi request lấy thông tin booking
    Log     Gửi GET request đến: ${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}
    ${api_response}=    GET    ${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}
    ...    headers=${headers_dict}       # Gửi kèm headers
    ...    expected_status=any           # Chấp nhận mọi status code để xử lý sau

    # BƯỚC 4: LOG RESPONSE ĐỂ DEBUG
    Log     Response status: ${api_response.status_code}
    Log     Response body: ${api_response.text}

    # BƯỚC 5: TRẢ VỀ RESPONSE
    RETURN    ${api_response}


# KEYWORD: XÁC THỰC RESPONSE GET BOOKING
# -----------------------------------------------------------------------------
Verify Get Booking Response
    [Documentation]
    ...    MỤC ĐÍCH: Xác thực response từ GET booking API
    ...
    ...    INPUT: api_response (Response object từ GET /booking/{id})
    ...
    ...    OUTPUT: Dictionary chứa dữ liệu booking đã được validate
    ...
    ...    VALIDATION:
    ...    - Status code phải là 200 (hoặc xử lý lỗi 404)
    ...    - Response phải có format JSON hợp lệ
    ...    - Phải chứa tất cả các field bắt buộc của booking
    ...
    ...    ERROR HANDLING:
    ...    - 404: Booking không tồn tại
    ...    - Khác 200: Lỗi server hoặc request không hợp lệ

    [Arguments]    ${api_response}

    # BƯỚC 1: XÁC THỰC STATUS CODE VỚI ERROR HANDLING
    Log     Kiểm tra status code của GET response

    # Xử lý các trường hợp lỗi phổ biến
    IF    ${api_response.status_code} == 404
        Fail     Booking không tồn tại (404 Not Found)
    ELSE IF    ${api_response.status_code} != 200
        Fail     API call thất bại với status code: ${api_response.status_code}
    END

    Log     Status code hợp lệ: ${api_response.status_code}

    # BƯỚC 2: PARSE JSON RESPONSE
    # Chuyển response body thành dictionary để xử lý
    ${response_body_dict}=    Set Variable    ${api_response.json()}
    Log     Đã parse JSON response thành dictionary

    # BƯỚC 3: XÁC THỰC CÁC FIELD BẮT BUỘC
    # Kiểm tra response có chứa tất cả field cần thiết của booking
    Log     Kiểm tra các field bắt buộc trong response

    # Kiểm tra thông tin cơ bản
    Dictionary Should Contain Key    ${response_body_dict}    firstname
    Dictionary Should Contain Key    ${response_body_dict}    lastname
    Dictionary Should Contain Key    ${response_body_dict}    totalprice
    Dictionary Should Contain Key    ${response_body_dict}    depositpaid

    # Kiểm tra thông tin ngày tháng
    Dictionary Should Contain Key    ${response_body_dict}    bookingdates
    Dictionary Should Contain Key    ${response_body_dict}[bookingdates]    checkin
    Dictionary Should Contain Key    ${response_body_dict}[bookingdates]    checkout

    Log     Tất cả field bắt buộc đều có trong response

    # BƯỚC 4: LOG THÔNG TIN BOOKING
    ${customer_name}=    Set Variable    ${response_body_dict}[firstname] ${response_body_dict}[lastname]
    Log    ✅ Xác thực thành công cho booking của: ${customer_name}

    # BƯỚC 5: TRẢ VỀ DỮ LIỆU ĐÃ VALIDATE
    RETURN    ${response_body_dict}

# KEYWORD: GHI DỮ LIỆU BOOKING VÀO EXCEL
# -----------------------------------------------------------------------------
Write Booking Data To Excel
    [Documentation]
    ...    MỤC ĐÍCH: Ghi dữ liệu booking vào Excel sheet mới với format có cấu trúc
    ...
    ...    INPUT:
    ...    - validated_booking_data: Dictionary chứa dữ liệu booking đã được validate
    ...    - booking_id: ID của booking để ghi vào Excel
    ...
    ...    QUY TRÌNH:
    ...    1. Mở file Excel hiện có
    ...    2. Tạo sheet mới tên "bookingid" (hoặc chuyển đến sheet đã có)
    ...    3. Ghi header cho sheet (Field | Value)
    ...    4. Ghi từng field của booking vào các hàng
    ...    5. Lưu và đóng file Excel
    ...
    ...    OUTPUT FORMAT:
    ...    | Field          | Value           |
    ...    | Booking ID     | 1187            |
    ...    | First Name     | John            |
    ...    | Last Name      | Smith           |
    ...    | Total Price    | 111             |
    ...    | Deposit Paid   | True            |
    ...    | Check-in Date  | 2018-01-01      |
    ...    | Check-out Date | 2019-01-01      |
    ...    | Additional Needs| Breakfast      |

    [Arguments]    ${validated_booking_data}    ${booking_id}

    # BƯỚC 1: MỞ FILE EXCEL
    Log    📂 Mở file Excel: ${EXCEL_FILE}
    Open Workbook    ${EXCEL_FILE}

    # BƯỚC 2: TẠO HOẶC CHUYỂN ĐẾN SHEET "BOOKINGID"
    # Sử dụng TRY-EXCEPT để xử lý trường hợp sheet đã tồn tại
    Log    📋 Chuẩn bị sheet 'bookingid' để ghi dữ liệu
    TRY
        # Thử tạo sheet mới
        Create Sheet    bookingid
        Log    ✅ Đã tạo sheet mới: bookingid
        # Lưu ý: Sau khi tạo sheet mới, nó sẽ tự động trở thành sheet active

    EXCEPT    AS    ${error_message}
        # Nếu sheet đã tồn tại, chuyển đến sheet đó
        Log    ⚠️ Sheet bookingid đã tồn tại: ${error_message}
        Switch Sheet    bookingid
        Log    ✅ Đã chuyển đến sheet: bookingid
    END

    # BƯỚC 3: GHI HEADER CHO SHEET
    # Tạo header để dễ đọc: Field | Value
    Log    📝 Ghi header cho sheet
    Write To Cell    A1    Field
    Write To Cell    B1    Value
    Log    ✅ Đã ghi header vào hàng 1

    # BƯỚC 4: GHI DỮ LIỆU BOOKING THEO TỪNG HÀNG
    # Sử dụng biến row để theo dõi hàng hiện tại
    Log    📝 Bắt đầu ghi dữ liệu booking vào sheet

    # Khởi tạo số hàng (bắt đầu từ hàng 2 vì hàng 1 là header)
    ${current_row}=    Set Variable    2

    # Ghi Booking ID
    Write To Cell    A${current_row}    Booking ID
    Write To Cell    B${current_row}    ${booking_id}
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi First Name
    Write To Cell    A${current_row}    First Name
    Write To Cell    B${current_row}    ${validated_booking_data}[firstname]
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi Last Name
    Write To Cell    A${current_row}    Last Name
    Write To Cell    B${current_row}    ${validated_booking_data}[lastname]
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi Total Price
    Write To Cell    A${current_row}    Total Price
    Write To Cell    B${current_row}    ${validated_booking_data}[totalprice]
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi Deposit Paid
    Write To Cell    A${current_row}    Deposit Paid
    Write To Cell    B${current_row}    ${validated_booking_data}[depositpaid]
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi Check-in Date
    Write To Cell    A${current_row}    Check-in Date
    Write To Cell    B${current_row}    ${validated_booking_data}[bookingdates][checkin]
    ${current_row}=    Evaluate    ${current_row} + 1

    # Ghi Check-out Date
    Write To Cell    A${current_row}    Check-out Date
    Write To Cell    B${current_row}    ${validated_booking_data}[bookingdates][checkout]
    ${current_row}=    Evaluate    ${current_row} + 1

    # BƯỚC 5: GHI ADDITIONAL NEEDS (NẾU CÓ)
    # Kiểm tra xem có additional needs không trước khi ghi
    IF    'additionalneeds' in ${validated_booking_data}
        Write To Cell    A${current_row}    Additional Needs
        Write To Cell    B${current_row}    ${validated_booking_data}[additionalneeds]
        Log    ✅ Đã ghi Additional Needs: ${validated_booking_data}[additionalneeds]
    ELSE
        Log    ℹ️ Booking này không có Additional Needs
    END

    # BƯỚC 6: LƯU VÀ ĐÓNG FILE EXCEL
    Log    💾 Lưu file Excel
    Save

    Log    📂 Đóng file Excel
    Close Workbook

    # BƯỚC 7: LOG KẾT QUẢ THÀNH CÔNG
    ${customer_name}=    Set Variable    ${validated_booking_data}[firstname] ${validated_booking_data}[lastname]
    Log    🎉 Đã ghi thành công dữ liệu booking ID ${booking_id} của ${customer_name} vào sheet 'bookingid'


# =============================================================================
# KẾT THÚC FILE - TỔNG KẾT VỀ NHỮNG GÌ ĐÃ HỌC
# =============================================================================
#
# 🎓 KIẾN THỨC ĐÃ HỌC TRONG FILE NÀY:
#
# 1. ROBOT FRAMEWORK CƠ BẢN:
#    - Cấu trúc file .robot (Settings, Variables, Test Cases, Keywords)
#    - Cách import thư viện và sử dụng
#    - Cách viết test case và custom keyword
#    - Sử dụng FOR loop và IF-ELSE
#    - Error handling với TRY-EXCEPT
#
# 2. API TESTING:
#    - Gửi POST request để tạo dữ liệu
#    - Gửi GET request để lấy dữ liệu
#    - Xử lý JSON payload và response
#    - Validation status code và response data
#    - Error handling cho các HTTP status code
#
# 3. EXCEL AUTOMATION:
#    - Đọc dữ liệu từ Excel với header
#    - Tạo và quản lý Excel sheet
#    - Ghi dữ liệu vào Excel theo format có cấu trúc
#    - Quản lý workbook (mở, lưu, đóng)
#
# 4. BEST PRACTICES:
#    - Sử dụng meaningful variable names
#    - Comprehensive logging cho debugging
#    - Proper error handling và validation
#    - Clean code structure với comments
#    - Resource cleanup (đóng workbook)
#
# 🚀 BƯỚC TIẾP THEO:
# - Thử chạy test với: robot create_booking_suite.robot
# - Xem log file để hiểu flow execution
# - Thử modify test data trong Excel
# - Experiment với các API endpoint khác
#
# =============================================================================
